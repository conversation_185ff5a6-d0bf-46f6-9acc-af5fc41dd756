@import url("https://fonts.googleapis.com/css2?family=Sour+Gummy:ital,wght@0,100..900;1,100..900&display=swap");

@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;

  box-sizing: border-box;
}

@layer base {
  :root {
    --background: #121212;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 240 5.9% 10%;
    --primary-foreground: 0 0% 98%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 240 10% 3.9%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
  }
  .dark {
    --background: #121212;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 240 5.9% 10%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 240 3.7% 15.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 240 4.9% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  border: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

@layer components {
  .timer-container {
    @apply h-screen w-full flex flex-col relative;
  }

  .phase-indicator {
    @apply absolute bottom-0 left-0 right-0 h-2 bg-neutral-800/50 backdrop-blur-sm;
  }
  .chess-timer {
    @apply min-h-screen w-full flex flex-col relative overflow-hidden;
  }

  .timer-display {
    @apply text-[4rem] xs:text-[6rem] sm:text-[8rem] md:text-[10rem] lg:text-[14rem] 
           font-bold font-digital transition-colors duration-300 tracking-wider;
  }

  .action-buttons {
    @apply fixed bottom-4 left-1/2 transform -translate-x-1/2 
           flex items-center justify-center space-x-4 z-50;
  }

  .phase-timeline {
    @apply absolute bottom-0 left-0 right-0 h-4 bg-neutral-800/50 
           backdrop-blur-sm overflow-hidden;
  }

  .progress-bar {
    @apply h-full bg-gradient-to-r from-green-500 via-yellow-500 to-red-500;
  }

  .phase-marker {
    @apply absolute top-1/2 -translate-y-1/2 flex flex-col items-center 
           transition-all duration-300 cursor-pointer;
  }

  .marker-dot {
    @apply w-3 h-3 rounded-full bg-current;
  }

  .marker-label {
    @apply text-xs font-medium mt-1 whitespace-nowrap;
  }
}
