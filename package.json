{"name": "chess-timer", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.6", "canvas-confetti": "^1.9.3", "chart.js": "^4.4.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^11.12.0", "lucide-react": "^0.456.0", "motion": "^11.12.0", "next": "^15.3.4", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "use-sound": "^4.0.3", "zustand": "^5.0.1"}, "devDependencies": {"@types/canvas-confetti": "^1.6.4", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}