[{"C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ChessTimer.tsx": "3", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\GameSummary\\index.tsx": "4", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\GameSummary.tsx": "5", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\GestureHelpDialog.tsx": "6", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\KeyboardShortcuts.tsx": "7", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\MoveHistoryModal.tsx": "8", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\stats\\PhaseDistribution.tsx": "9", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\stats\\PlayerTimingStats.tsx": "10", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\stats\\TimeComparison.tsx": "11", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\TimeGraph.tsx": "12", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\ActionButton.tsx": "13", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\button.tsx": "14", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\card.tsx": "15", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\CustomTooltip.tsx": "16", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\progress.tsx": "17", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\slider.tsx": "18", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\tooltip.tsx": "19", "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\lib\\utils.ts": "20"}, {"size": 1071, "mtime": 1751131946838, "results": "21", "hashOfConfig": "22"}, {"size": 7717, "mtime": 1751131946840, "results": "23", "hashOfConfig": "22"}, {"size": 24595, "mtime": 1751165259161, "results": "24", "hashOfConfig": "22"}, {"size": 1, "mtime": 1751131946844, "results": "25", "hashOfConfig": "22"}, {"size": 12881, "mtime": 1751131946842, "results": "26", "hashOfConfig": "22"}, {"size": 2288, "mtime": 1751165268859, "results": "27", "hashOfConfig": "22"}, {"size": 929, "mtime": 1751133751363, "results": "28", "hashOfConfig": "22"}, {"size": 5699, "mtime": 1751131946847, "results": "29", "hashOfConfig": "22"}, {"size": 4163, "mtime": 1751131946850, "results": "30", "hashOfConfig": "22"}, {"size": 5508, "mtime": 1751131946850, "results": "31", "hashOfConfig": "22"}, {"size": 2457, "mtime": 1751131946850, "results": "32", "hashOfConfig": "22"}, {"size": 5159, "mtime": 1751131946847, "results": "33", "hashOfConfig": "22"}, {"size": 2090, "mtime": 1751132939141, "results": "34", "hashOfConfig": "22"}, {"size": 1959, "mtime": 1751131946853, "results": "35", "hashOfConfig": "22"}, {"size": 1904, "mtime": 1751131946855, "results": "36", "hashOfConfig": "22"}, {"size": 1236, "mtime": 1751131946853, "results": "37", "hashOfConfig": "22"}, {"size": 820, "mtime": 1751131946855, "results": "38", "hashOfConfig": "22"}, {"size": 1079, "mtime": 1751131946857, "results": "39", "hashOfConfig": "22"}, {"size": 1249, "mtime": 1751131946857, "results": "40", "hashOfConfig": "22"}, {"size": 172, "mtime": 1751131946857, "results": "41", "hashOfConfig": "22"}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1lk1lph", {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ChessTimer.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\GameSummary\\index.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\GameSummary.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\GestureHelpDialog.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\KeyboardShortcuts.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\MoveHistoryModal.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\stats\\PhaseDistribution.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\stats\\PlayerTimingStats.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\stats\\TimeComparison.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\TimeGraph.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\ActionButton.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\CustomTooltip.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\slider.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\components\\ui\\tooltip.tsx", [], [], "C:\\Users\\<USER>\\utkarsh\\chess-clock\\src\\lib\\utils.ts", [], []]